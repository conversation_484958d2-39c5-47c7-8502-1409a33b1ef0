package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"regexp"
	"syscall"
	"time"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// UserStoryInput represents the input for the user story analysis tool
type UserStoryInput struct {
	Prompt string `json:"prompt" jsonschema:"the prompt text to analyze for user stories"`
}

// UserStoryOutput represents the output of the user story analysis
type UserStoryOutput struct {
	RawPrompt string `json:"rawPrompt,omitempty" jsonschema:"the raw unmodified prompt if user story detected"`
}

// UserStoryDetector analyzes text to determine if it contains user stories
type UserStoryDetector struct {
	userStoryPatterns   []*regexp.Regexp
	userStoryKeywords   []string
	confidenceThreshold float64
}

// AnalyzeUserStory is the tool handler for user story analysis
func AnalyzeUserStory(ctx context.Context, req *mcp.CallToolRequest, input UserStoryInput) (
	*mcp.CallToolResult,
	UserStoryOutput,
	error,
) {
	output := UserStoryOutput{
		RawPrompt: input.Prompt,
	}

	return nil, output, nil
}

func main() {
	var (
		port = flag.String("port", "8080", "Port to listen on")
		host = flag.String("host", "localhost", "Host to bind to")
	)
	flag.Parse()

	// Create a server with user story analysis tool
	server := mcp.NewServer(&mcp.Implementation{
		Name:    "Go MCP User Story Server",
		Version: "1.0.0",
	}, nil)

	// Add the user story analysis tool
	mcp.AddTool(server, &mcp.Tool{
		Name:        "analyze_user_story",
		Description: "Analyzes a prompt to detect if it contains user stories and returns the raw prompt if detected",
	}, AnalyzeUserStory)

	// Create HTTP handler for Streamable HTTP transport
	handler := mcp.NewStreamableHTTPHandler(func(request *http.Request) *mcp.Server {
		return server
	}, &mcp.StreamableHTTPOptions{
		Stateless:    false, // We want stateful sessions
		JSONResponse: false, // Use SSE streaming
	})

	// Set up HTTP server
	addr := fmt.Sprintf("%s:%s", *host, *port)
	httpServer := &http.Server{
		Addr:    addr,
		Handler: handler,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting MCP server on http://%s", addr)
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := httpServer.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Note: StreamableHTTPHandler doesn't have a Close method
	// Sessions are automatically cleaned up when the HTTP server shuts down

	log.Println("Server exited")
}
