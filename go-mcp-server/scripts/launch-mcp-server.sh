#!/bin/bash

# Launch script for Go MCP User Story Server
# This script can be used by GitHub Copilot or other MCP clients

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_DIR="$(dirname "$SCRIPT_DIR")"

# Change to the server directory
cd "$SERVER_DIR"

# Build the server if it doesn't exist or if main.go is newer
if [ ! -f "mcp-server" ] || [ "main.go" -nt "mcp-server" ]; then
    echo "Building MCP server..." >&2
    go build -o mcp-server .
    if [ $? -ne 0 ]; then
        echo "Failed to build MCP server" >&2
        exit 1
    fi
fi

# Default values
HOST="localhost"
PORT="8080"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -host)
            HOST="$2"
            shift 2
            ;;
        -port)
            PORT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [-host HOST] [-port PORT]"
            echo "  -host HOST    Host to bind to (default: localhost)"
            echo "  -port PORT    Port to listen on (default: 8080)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1" >&2
            exit 1
            ;;
    esac
done

# Launch the server
echo "Starting Go MCP User Story Server on $HOST:$PORT..." >&2
exec ./mcp-server -host "$HOST" -port "$PORT"
