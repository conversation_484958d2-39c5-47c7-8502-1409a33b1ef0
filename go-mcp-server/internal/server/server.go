package server

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"

	"go-mcp-server/internal/mcp"
	"go-mcp-server/internal/userstory"
)

// Constants
const (
	HeaderSessionID       = "Mcp-Session-Id"
	HeaderProtocolVersion = "MCP-Protocol-Version"
	HeaderContentType     = "Content-Type"
	HeaderLastEventID     = "Last-Event-ID"

	ContentTypeJSON = "application/json"
	ContentTypeSSE  = "text/event-stream"

	ProtocolVersion = "2025-06-18"
)

// MCPServer implements the MCP protocol over HTTP with SSE support
type MCPServer struct {
	router            *mux.Router
	sessions          map[string]*Session
	sessionsMutex     sync.RWMutex
	userStoryDetector *userstory.UserStoryDetector
	serverInfo        mcp.ServerInfo
	capabilities      mcp.ServerCapabilities
}

// Session represents a client session
type Session struct {
	ID           string
	CreatedAt    time.Time
	LastActivity time.Time
	Initialized  bool
	ClientInfo   *mcp.ClientInfo
	SSEClients   map[string]*SSEClient
	mutex        sync.RWMutex
}

// SSEClient represents a Server-Sent Events connection
type SSEClient struct {
	ID          string
	Writer      http.ResponseWriter
	Flusher     http.Flusher
	Done        chan bool
	LastEventID string
}

// NewMCPServer creates a new MCP server instance
func NewMCPServer() *MCPServer {
	server := &MCPServer{
		router:            mux.NewRouter(),
		sessions:          make(map[string]*Session),
		userStoryDetector: userstory.NewUserStoryDetector(),
		serverInfo: mcp.ServerInfo{
			Name:    "Go MCP User Story Server",
			Version: "1.0.0",
		},
		capabilities: mcp.ServerCapabilities{
			Tools: &mcp.ToolsCapability{
				ListChanged: false,
			},
		},
	}

	server.setupRoutes()
	return server
}

// setupRoutes configures the HTTP routes
func (s *MCPServer) setupRoutes() {
	// MCP endpoint - handles both POST and GET
	s.router.HandleFunc("/mcp", s.handleMCPEndpoint).Methods("POST", "GET", "DELETE")

	// Health check endpoint
	s.router.HandleFunc("/health", s.handleHealth).Methods("GET")
}

// ServeHTTP implements http.Handler interface
func (s *MCPServer) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Add CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", fmt.Sprintf("%s, Accept, %s, %s, %s", HeaderContentType, HeaderSessionID, HeaderProtocolVersion, HeaderLastEventID))

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	s.router.ServeHTTP(w, r)
}

// handleMCPEndpoint handles the main MCP endpoint
func (s *MCPServer) handleMCPEndpoint(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case "POST":
		s.handlePOST(w, r)
	case "GET":
		s.handleGET(w, r)
	case "DELETE":
		s.handleDELETE(w, r)
	}
}

// handlePOST processes JSON-RPC messages from clients
func (s *MCPServer) handlePOST(w http.ResponseWriter, r *http.Request) {
	// Read request body
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Failed to read request body", http.StatusBadRequest)
		return
	}

	// Parse JSON-RPC message
	var message map[string]interface{}
	if err := json.Unmarshal(body, &message); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Check if it's a request, response, or notification
	if id, hasID := message["id"]; hasID {
		// It's a request - handle it and potentially start SSE stream
		s.handleJSONRPCRequest(w, r, message, id)
	} else {
		// It's a notification or response - handle and return 202
		s.handleJSONRPCNotification(w, r, message)
		w.WriteHeader(http.StatusAccepted)
	}
}

// handleGET opens an SSE stream for server-to-client communication
func (s *MCPServer) handleGET(w http.ResponseWriter, r *http.Request) {
	sessionID := r.Header.Get(HeaderSessionID)
	if sessionID == "" {
		http.Error(w, fmt.Sprintf("Missing %s header", HeaderSessionID), http.StatusBadRequest)
		return
	}

	session := s.getSession(sessionID)
	if session == nil {
		http.Error(w, "Session not found", http.StatusNotFound)
		return
	}

	// Set SSE headers
	w.Header().Set(HeaderContentType, ContentTypeSSE)
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")

	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
		return
	}

	// Create SSE client
	clientID := uuid.New().String()
	sseClient := &SSEClient{
		ID:          clientID,
		Writer:      w,
		Flusher:     flusher,
		Done:        make(chan bool),
		LastEventID: r.Header.Get("Last-Event-ID"),
	}

	// Add client to session
	session.mutex.Lock()
	session.SSEClients[clientID] = sseClient
	session.mutex.Unlock()

	// Keep connection alive
	ctx := r.Context()
	for {
		select {
		case <-ctx.Done():
			s.removeSSEClient(sessionID, clientID)
			return
		case <-sseClient.Done:
			s.removeSSEClient(sessionID, clientID)
			return
		case <-time.After(30 * time.Second):
			// Send keep-alive ping
			fmt.Fprintf(w, ": ping\n\n")
			flusher.Flush()
		}
	}
}

// handleDELETE terminates a session
func (s *MCPServer) handleDELETE(w http.ResponseWriter, r *http.Request) {
	sessionID := r.Header.Get("Mcp-Session-Id")
	if sessionID == "" {
		http.Error(w, "Missing Mcp-Session-Id header", http.StatusBadRequest)
		return
	}

	s.sessionsMutex.Lock()
	delete(s.sessions, sessionID)
	s.sessionsMutex.Unlock()

	w.WriteHeader(http.StatusOK)
}

// handleHealth provides a health check endpoint
func (s *MCPServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "healthy",
		"server":  s.serverInfo.Name,
		"version": s.serverInfo.Version,
	})
}

// Helper methods

func (s *MCPServer) getSession(sessionID string) *Session {
	s.sessionsMutex.RLock()
	defer s.sessionsMutex.RUnlock()
	return s.sessions[sessionID]
}

func (s *MCPServer) createSession() *Session {
	sessionID := uuid.New().String()
	session := &Session{
		ID:           sessionID,
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
		Initialized:  false,
		SSEClients:   make(map[string]*SSEClient),
	}

	s.sessionsMutex.Lock()
	s.sessions[sessionID] = session
	s.sessionsMutex.Unlock()

	return session
}

func (s *MCPServer) removeSSEClient(sessionID, clientID string) {
	session := s.getSession(sessionID)
	if session != nil {
		session.mutex.Lock()
		if client, exists := session.SSEClients[clientID]; exists {
			close(client.Done)
			delete(session.SSEClients, clientID)
		}
		session.mutex.Unlock()
	}
}

// handleJSONRPCRequest processes JSON-RPC requests
func (s *MCPServer) handleJSONRPCRequest(w http.ResponseWriter, r *http.Request, message map[string]interface{}, id interface{}) {
	method, ok := message["method"].(string)
	if !ok {
		s.sendJSONRPCError(w, id, -32600, "Invalid Request", "Missing method")
		return
	}

	var params interface{}
	if p, exists := message["params"]; exists {
		params = p
	}

	switch method {
	case "initialize":
		s.handleInitialize(w, r, id, params)
	case "tools/list":
		s.handleListTools(w, r, id, params)
	case "tools/call":
		s.handleCallTool(w, r, id, params)
	default:
		s.sendJSONRPCError(w, id, -32601, "Method not found", method)
	}
}

// handleJSONRPCNotification processes JSON-RPC notifications
func (s *MCPServer) handleJSONRPCNotification(w http.ResponseWriter, r *http.Request, message map[string]interface{}) {
	method, ok := message["method"].(string)
	if !ok {
		return
	}

	switch method {
	case "initialized":
		s.handleInitialized(r, message["params"])
	case "notifications/cancelled":
		// Handle cancellation
		log.Println("Received cancellation notification")
	}
}

// handleInitialize processes the initialize request
func (s *MCPServer) handleInitialize(w http.ResponseWriter, r *http.Request, id interface{}, params interface{}) {
	var initReq mcp.InitializeRequest
	if params != nil {
		paramBytes, _ := json.Marshal(params)
		if err := json.Unmarshal(paramBytes, &initReq); err != nil {
			s.sendJSONRPCError(w, id, -32602, "Invalid params", err.Error())
			return
		}
	}

	// Create new session
	session := s.createSession()

	// Prepare response
	result := mcp.InitializeResult{
		ProtocolVersion: "2025-06-18",
		Capabilities:    s.capabilities,
		ServerInfo:      s.serverInfo,
	}

	// Set session header
	w.Header().Set("Mcp-Session-Id", session.ID)
	w.Header().Set("MCP-Protocol-Version", "2025-06-18")

	s.sendJSONRPCResponse(w, id, result)
}

// handleInitialized processes the initialized notification
func (s *MCPServer) handleInitialized(r *http.Request, params interface{}) {
	sessionID := r.Header.Get("Mcp-Session-Id")
	if sessionID == "" {
		return
	}

	session := s.getSession(sessionID)
	if session != nil {
		session.mutex.Lock()
		session.Initialized = true
		session.LastActivity = time.Now()
		session.mutex.Unlock()
	}
}

// handleListTools returns the list of available tools
func (s *MCPServer) handleListTools(w http.ResponseWriter, r *http.Request, id interface{}, params interface{}) {
	tools := []mcp.Tool{
		{
			Name:        "analyze_user_story",
			Description: "Analyzes a prompt to detect if it contains user stories and returns the raw prompt if detected",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"prompt": map[string]interface{}{
						"type":        "string",
						"description": "The prompt text to analyze for user stories",
					},
				},
				"required": []string{"prompt"},
			},
		},
	}

	result := mcp.ListToolsResult{Tools: tools}
	s.sendJSONRPCResponse(w, id, result)
}

// handleCallTool processes tool calls
func (s *MCPServer) handleCallTool(w http.ResponseWriter, r *http.Request, id interface{}, params interface{}) {
	var callReq mcp.CallToolRequest
	if params != nil {
		paramBytes, _ := json.Marshal(params)
		if err := json.Unmarshal(paramBytes, &callReq); err != nil {
			s.sendJSONRPCError(w, id, -32602, "Invalid params", err.Error())
			return
		}
	}

	switch callReq.Name {
	case "analyze_user_story":
		s.handleAnalyzeUserStory(w, id, callReq.Arguments)
	default:
		s.sendJSONRPCError(w, id, -32601, "Tool not found", callReq.Name)
	}
}

// handleAnalyzeUserStory processes the user story analysis tool
func (s *MCPServer) handleAnalyzeUserStory(w http.ResponseWriter, id interface{}, args map[string]interface{}) {
	prompt, ok := args["prompt"].(string)
	if !ok {
		s.sendJSONRPCError(w, id, -32602, "Invalid arguments", "prompt must be a string")
		return
	}

	// Detect if the prompt contains user stories
	isUserStory := s.userStoryDetector.IsUserStory(prompt)
	details := s.userStoryDetector.GetDetectionDetails(prompt)

	var content []mcp.Content
	if isUserStory {
		// Return the raw unmodified prompt if it's detected as a user story
		content = []mcp.Content{
			mcp.NewTextContent(fmt.Sprintf("User story detected! Raw prompt: %s", prompt)),
			mcp.NewTextContent(fmt.Sprintf("Detection details: %+v", details)),
		}
	} else {
		content = []mcp.Content{
			mcp.NewTextContent("No user story detected in the provided prompt."),
			mcp.NewTextContent(fmt.Sprintf("Detection details: %+v", details)),
		}
	}

	result := mcp.CallToolResult{
		Content: content,
		IsError: false,
	}

	s.sendJSONRPCResponse(w, id, result)
}

// Helper methods for JSON-RPC responses
func (s *MCPServer) sendJSONRPCResponse(w http.ResponseWriter, id interface{}, result interface{}) {
	response := mcp.NewJSONRPCResponse(id, result)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func (s *MCPServer) sendJSONRPCError(w http.ResponseWriter, id interface{}, code int, message, data string) {
	response := mcp.NewJSONRPCError(id, code, message, data)
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusBadRequest)
	json.NewEncoder(w).Encode(response)
}
