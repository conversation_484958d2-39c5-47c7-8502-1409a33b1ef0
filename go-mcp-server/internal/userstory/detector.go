package userstory

import (
	"regexp"
	"strings"
)

// UserStoryDetector analyzes text to determine if it contains user stories
type UserStoryDetector struct {
	// Patterns that commonly indicate user stories
	userStoryPatterns []*regexp.Regexp
	// Keywords that often appear in user stories
	userStoryKeywords []string
	// Minimum confidence threshold (0.0 to 1.0)
	confidenceThreshold float64
}

// NewUserStoryDetector creates a new user story detector with default patterns
func NewUserStoryDetector() *UserStoryDetector {
	patterns := []*regexp.Regexp{
		// Classic user story format: "As a [role], I want [goal] so that [benefit]"
		regexp.MustCompile(`(?i)as\s+a\s+.+,?\s+i\s+(want|need|would\s+like)\s+.+\s+so\s+that\s+.+`),
		// Alternative format: "As [role], I want [goal] so that [benefit]"
		regexp.MustCompile(`(?i)as\s+.+,?\s+i\s+(want|need|would\s+like)\s+.+\s+so\s+that\s+.+`),
		// Simplified format: "I want [goal] as a [role]"
		regexp.MustCompile(`(?i)i\s+(want|need|would\s+like)\s+.+\s+as\s+a\s+.+`),
		// Feature format: "Feature: [title]" followed by user story
		regexp.MustCompile(`(?i)feature:\s*.+\s+as\s+a\s+.+`),
		// Story format: "Story: [title]" or "User Story: [title]"
		regexp.MustCompile(`(?i)(user\s+)?story:\s*.+`),
		// Acceptance criteria indicators
		regexp.MustCompile(`(?i)(given|when|then)\s+.+`),
		// Epic/Feature indicators with user story elements
		regexp.MustCompile(`(?i)(epic|feature):\s*.+\s+(as\s+a|i\s+want)`),
	}

	keywords := []string{
		"user story", "user stories", "acceptance criteria", "given", "when", "then",
		"as a user", "as an admin", "as a customer", "as a", "i want", "i need",
		"so that", "in order to", "feature", "epic", "story", "requirement",
		"use case", "scenario", "behavior", "functionality",
	}

	return &UserStoryDetector{
		userStoryPatterns:   patterns,
		userStoryKeywords:   keywords,
		confidenceThreshold: 0.3, // 30% confidence threshold
	}
}

// IsUserStory analyzes the given text and returns true if it appears to contain user stories
func (d *UserStoryDetector) IsUserStory(text string) bool {
	confidence := d.CalculateConfidence(text)
	return confidence >= d.confidenceThreshold
}

// CalculateConfidence returns a confidence score (0.0 to 1.0) indicating how likely
// the text contains user stories
func (d *UserStoryDetector) CalculateConfidence(text string) float64 {
	if strings.TrimSpace(text) == "" {
		return 0.0
	}

	var score float64
	textLower := strings.ToLower(text)

	// Check for user story patterns (high weight)
	patternMatches := 0
	for _, pattern := range d.userStoryPatterns {
		if pattern.MatchString(text) {
			patternMatches++
		}
	}
	
	// Pattern matches contribute heavily to confidence
	if patternMatches > 0 {
		score += 0.6 * float64(patternMatches) / float64(len(d.userStoryPatterns))
		if score > 0.6 {
			score = 0.6 // Cap pattern contribution at 60%
		}
	}

	// Check for user story keywords (medium weight)
	keywordMatches := 0
	for _, keyword := range d.userStoryKeywords {
		if strings.Contains(textLower, keyword) {
			keywordMatches++
		}
	}
	
	// Keyword matches contribute moderately
	keywordScore := 0.4 * float64(keywordMatches) / float64(len(d.userStoryKeywords))
	if keywordScore > 0.4 {
		keywordScore = 0.4 // Cap keyword contribution at 40%
	}
	score += keywordScore

	// Additional heuristics
	
	// Check for structured format indicators
	if d.hasStructuredFormat(textLower) {
		score += 0.2
	}

	// Check for multiple sentences (user stories are usually descriptive)
	sentences := strings.Split(text, ".")
	if len(sentences) >= 2 {
		score += 0.1
	}

	// Check for role-based language
	if d.hasRoleBasedLanguage(textLower) {
		score += 0.1
	}

	// Ensure score doesn't exceed 1.0
	if score > 1.0 {
		score = 1.0
	}

	return score
}

// hasStructuredFormat checks for structured user story formats
func (d *UserStoryDetector) hasStructuredFormat(text string) bool {
	structuredIndicators := []string{
		"given", "when", "then",
		"feature:", "story:", "user story:",
		"acceptance criteria", "scenario:",
	}

	for _, indicator := range structuredIndicators {
		if strings.Contains(text, indicator) {
			return true
		}
	}
	return false
}

// hasRoleBasedLanguage checks for role-based language common in user stories
func (d *UserStoryDetector) hasRoleBasedLanguage(text string) bool {
	roleIndicators := []string{
		"as a", "as an", "user", "customer", "admin", "administrator",
		"manager", "developer", "visitor", "guest", "member",
	}

	for _, indicator := range roleIndicators {
		if strings.Contains(text, indicator) {
			return true
		}
	}
	return false
}

// GetDetectionDetails returns detailed information about why text was classified as a user story
func (d *UserStoryDetector) GetDetectionDetails(text string) map[string]interface{} {
	confidence := d.CalculateConfidence(text)
	isUserStory := confidence >= d.confidenceThreshold
	
	textLower := strings.ToLower(text)
	
	// Find matching patterns
	matchingPatterns := []string{}
	for i, pattern := range d.userStoryPatterns {
		if pattern.MatchString(text) {
			matchingPatterns = append(matchingPatterns, pattern.String())
		}
	}
	
	// Find matching keywords
	matchingKeywords := []string{}
	for _, keyword := range d.userStoryKeywords {
		if strings.Contains(textLower, keyword) {
			matchingKeywords = append(matchingKeywords, keyword)
		}
	}

	return map[string]interface{}{
		"isUserStory":        isUserStory,
		"confidence":         confidence,
		"threshold":          d.confidenceThreshold,
		"matchingPatterns":   matchingPatterns,
		"matchingKeywords":   matchingKeywords,
		"hasStructuredFormat": d.hasStructuredFormat(textLower),
		"hasRoleBasedLanguage": d.hasRoleBasedLanguage(textLower),
	}
}
